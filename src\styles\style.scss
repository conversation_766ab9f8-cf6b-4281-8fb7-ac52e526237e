@use "./abstracts" as *;
@use "./components/buttons";
@use "./components/cards";
@use "./components/input";
@use "./components/table";
@use "./components/uploadBox";
@use "./components/commonModals";
@use "./components/editor";
@use "./components/animation";
@use "./components/userWalkthrough";
@import url("https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:ital,wght@0,200..800;1,200..800&display=swap");

* {
  font-family: "Plus Jakarta Sans", sans-serif;
  font-optical-sizing: auto;
  font-style: normal;
}

html,
body {
  font-size: 62.5%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
}
.main-body {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.scroll-content {
  flex: 1;
  overflow-y: auto !important;
}
// Temp Solution for Sun Job Editor bold and italic issue fix
.sun-editor-editable {
  strong {
    font-weight: bold;
  }
  em {
    font-style: italic;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0px;
  padding: 0px;
}

p {
  font-size: $text-md;
  margin: 0px;
  padding: 0px;
  font-weight: 300;
  @media only screen and (max-width: 767px) {
    font-size: $text-md;
  }
}

a {
  font-size: $text-md;
  cursor: pointer;
  text-decoration: none !important;
  @media only screen and (max-width: 767px) {
    font-size: $text-md;
  }
}

//common font size style ----------
.font12 {
  font-size: $text-xs !important;
}

.font14 {
  font-size: $text-sm !important;
}

.font16 {
  font-size: $text-md !important;
}

.font18 {
  font-size: $text-lg !important;
}

//color style ----------
.color-primary {
  color: $primary !important;
}

.color-success {
  color: $green !important;
}

.color-dark {
  color: $dark !important;
}

.color-white {
  color: $white !important;
}

.color-secondary {
  color: $secondary !important;
}

.color-danger {
  color: $danger !important;
}

//interactions styles -----------
.cursor-pointer {
  cursor: pointer;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
input[type=file], /* FF, IE7+, chrome (except button) */
  input[type=file]::-webkit-file-upload-button {
  /* chromes and blink button */
  cursor: pointer;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
  appearance: textfield;
}

//common scrollbar style ----------
* {
  /* width */
  &::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    background-color: #e8e0ff;
    border-radius: 10px;
  }

  /* Track */
  &::-webkit-scrollbar-track {
    border-radius: 10px;
  }

  /* Handle */
  &::-webkit-scrollbar-thumb {
    background: $primary;
    border-radius: 10px;
  }

  /* Handle on hover */
  &::-webkit-scrollbar-thumb:hover {
    background: $primary;
  }
}
.email-text {
  color: #333;
  font-weight: $regular !important;
  &:hover {
    text-decoration: underline !important;
    cursor: pointer;
  }
}
.static-page {
  padding: 40px 0;
  h1 {
    margin-bottom: 30px;
    font-size: 2.4rem;
    font-weight: $bold;
    color: $dark;
  }
  h3 {
    margin-bottom: 10px;
    margin-top: 20px;
    font-size: 1.6rem;
    font-weight: $bold;
    color: $dark;
  }
  p {
    margin: 0;
    font-size: 1.6rem;
    font-weight: $regular;
    color: $dark;
  }
}
.logo-header {
  padding: 20px;
  position: sticky;
  top: 0;
  background-color: $white;
  box-shadow:
    0px 24px 7px 0px rgba(0, 0, 0, 0),
    0px 15px 6px 0px rgba(0, 0, 0, 0.01),
    0px 9px 5px 0px rgba(0, 0, 0, 0.02),
    0px 4px 4px 0px rgba(0, 0, 0, 0.03),
    0px 1px 2px 0px rgba(0, 0, 0, 0.04),
    0px 0px 0px 0px rgba(0, 0, 0, 0.04);
  img {
    width: 170px;
    height: auto;
    object-fit: contain;
  }
}
// logout header styles
.logout-header {
  margin-top: -90px;
  height: calc(100vh + 90px);
  .scroll-content {
    overflow: auto;
    height: 100vh;
  }

  .scrolling {
    backdrop-filter: blur(10px);
    background: $white;
    box-shadow:
      0px 24px 7px 0px rgba(0, 0, 0, 0),
      0px 15px 6px 0px rgba(0, 0, 0, 0.01),
      0px 9px 5px 0px rgba(0, 0, 0, 0.02),
      0px 4px 4px 0px rgba(0, 0, 0, 0.03),
      0px 1px 2px 0px rgba(0, 0, 0, 0.04),
      0px 0px 0px 0px rgba(0, 0, 0, 0.04);
    .logout-btns {
      .login-btn {
        background: $primary;
        color: $white;
        border-color: $primary;
        &.outline-btn {
          border-color: $primary;
          background-color: transparent;
          color: $primary;
          font-weight: $medium;
        }
      }
    }
  }
}

//button align style ----------
.button-align {
  display: flex;
  align-items: center;
  gap: 2rem;
  @media (max-width: 767px) {
    gap: 1.5rem;
    flex-wrap: wrap;
  }
}

//section spacing style ----------
.common-section-spacing {
  padding: 55px 0;
}

//skeleton style ----------
.skeleton-shimmer {
  background-color: #ebebeb !important;
  // color: #eee !important;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  z-index: 1;
  box-shadow: none;
  * {
    opacity: 0;
  }
}

.full-page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: $white;
  // filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  img {
    width: 100%;
    height: 400px;
    object-fit: contain;
    margin: auto;
  }
}
.skeleton-shimmer::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  height: 100%;
  width: 100%;
  background: linear-gradient(90deg, transparent, rgba(#f5f5f5, 0.9), transparent);
  animation: shimmer 1.5s infinite;
  z-index: 200;
}
.skeleton-shimmer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background: rgba(#ebebeb, 0.9);
  z-index: 100;
}

@keyframes shimmer {
  100% {
    left: 100%;
  }
}

//Tooltip style
.responsive-tooltip {
  z-index: 9999;
  font-size: $text-sm;

  background-color: $primary !important;
  color: $white !important;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
}
.react-tooltip {
  font-size: 10px !important;
}
.dotted-border {
  border-bottom: 1px dotted rgba($black, 0.5);
}

//common heading style ----------
.common-heading {
  h2 {
    color: $white;
    span {
      color: $secondary;
      font-size: $heading-md;
    }
  }
  p {
    font-size: $text-sm;
    color: $white;
    span {
      color: $secondary;
    }
  }
}
.section-heading {
  h2 {
    font-size: 2.4rem;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 20px;
    text-transform: capitalize;
    span {
      color: $secondary;
      &.primary {
        color: $primary;
      }
    }
  }
  h3 {
    font-size: 2.2rem;
    font-weight: $bold;
    color: $dark;
    margin-bottom: 20px;
    text-transform: capitalize;
  }
  p {
    font-size: $text-md;
    color: $dark;
    font-weight: $medium;
  }
  @media screen and (min-width: 992px) and (max-width: 1199px) {
    h2 {
      font-size: 2.4rem;
    }
  }
  @media screen and (max-width: 767px) {
    h2 {
      font-size: 2rem;
    }
  }
}

//avatar style ----------
.avatar-desc {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: $text-sm;
  img,
  svg {
    width: 35px;
    height: 35px;
    border-radius: 100px;
    border: 1px solid #cdcdcd;
  }
  svg {
    border: none;
    border-radius: 0;
  }
  .avatar-para {
    p {
      margin: 0;
      font-size: $text-md;
      font-weight: 500;
      &:first-child {
        font-size: 1rem;
        font-weight: 400;
      }
    }
  }
  .button-align {
    .user {
      color: #020202;
      font-weight: 400;
    }
    p {
      font-size: $text-md;
      margin: 0;
    }
    .date-share {
      display: flex;
      align-items: center;
      gap: 1rem;
      .dot {
        width: 4px;
        min-width: 4px;
        height: 4px;
        opacity: 0.5;
      }
      .date {
        opacity: 0.6;
      }
      button {
        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

//common box style ----------
.information-box {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 1rem;
  padding: 10px;
  background: $primary-gradient;
  p {
    font-size: $text-sm;
    color: $white;
    font-weight: $medium;
    margin: 0;
  }
}
.common-box {
  display: flex;
  gap: 20px;
  .main-content {
    flex: 1;
    width: 100%;
  }
}

//common page header style ----------
.common-page-header {
  padding: 40px 0;
  .breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
    a {
      color: $dark;
      font-size: $text-md;
      font-weight: $bold;
      margin: 0;
      &:not(:last-child) {
        color: rgba($dark, 0.7);
        font-weight: $medium;
        &::after {
          content: "/";
          margin: 0 2px;
        }
      }
    }
    a:hover {
      text-decoration: underline;
      color: $primary;
    }
  }
  //mobile
  @media screen and (max-width: 767px) {
    padding: 30px 0 20px 0;
  }
}
.common-page-head-section {
  .main-heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    h2 {
      font-size: 2.4rem;
      font-weight: $bold;
      color: $dark;
      margin: 0;
      span {
        color: $primary;
      }
      @media (width <= 767px) {
        font-size: $text-xxl;
      }
    }
    .right-action {
      display: flex;
      align-items: center;
      gap: 10px;
      justify-content: flex-end;
      .search-input {
        min-width: 400px;
      }
    }
  }
  .description {
    font-size: $text-md;
    color: $dark;
    padding-top: 15px;
    font-weight: $medium;
  }
  @media (width <= 991px) {
    .main-heading {
      align-items: flex-start;
      flex-direction: column;
      gap: 20px;
      padding: 0 4px;
      .right-action {
        width: 100%;
        .search-input {
          min-width: 55%;
        }
        .primary-btn {
          width: 100%;
        }
      }
    }
  }
  //min desktop
  @media screen and (min-width: 992px) and (max-width: 1199px) {
    .main-heading {
      h2 {
        font-size: 2.4rem;
      }
    }
  }
  //mobile
  @media screen and (max-width: 767px) {
    .main-heading {
      h2 {
        font-size: 2rem;
      }
    }
    .description {
      font-size: $text-sm;
    }
  }
}

//custom dropdown style
.custom-dropdown {
  position: absolute;
  top: 26px;
  right: 5px;
  background: #fff;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.12);
  border-radius: 12px;
  padding: 8px;
  min-width: 180px;
  z-index: 10;
  @extend %listSpacing;
  &.fromBottom {
    top: auto;
    bottom: 20px;
  }
  li {
    cursor: pointer;
    padding: 8px;
    border-bottom: 1px dashed rgba(11, 11, 11, 0.3);
    text-align: left;

    &:hover {
      color: $primary;
    }
    &:last-child {
      border-bottom: none;
    }
  }
}

//permissions card style ----------
.permissions-card {
  background: rgba($secondary, 0.1);
  border-radius: 20px;
  h3 {
    margin-bottom: 25px;
    font-weight: 700;
  }
  .checbox-group {
    padding: 15px 15px 15px 20px;
    margin: 10px 0;
    // background: rgba($commonAdminPrimary, 0.02);
    border-radius: 10px;
    @extend %listSpacing;
    &:last-child {
      margin-bottom: 0;
    }
    li {
      margin-bottom: 15px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    label {
      font-size: 1.4rem;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
      span {
        width: 16px;
        height: 16px;
        &::after {
          left: 4px;
          top: 1px;
        }
      }

      .permission-item {
        display: flex;
        flex-direction: column;
        margin-right: 20px;

        .permission-name {
          font-weight: 700;
          margin-bottom: 2px;
        }

        .permission-description {
          font-size: $text-xs;
          color: #666;
          font-weight: normal;
        }
      }
    }
  }
}
header {
  ul li:hover .strokeSvg {
    stroke: $primary !important;
  }
  .strokeSvg {
    stroke: $dark !important;
  }
}
// custom checkbox style start ---------
.container-checkbox {
  display: inline-block;
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  font-size: 1.4rem;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  color: $dark;
}

/* Hide the browser's default checkbox */
.container-checkbox input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

/* Create a custom checkbox */
.checkmark {
  position: absolute;
  top: 0;
  left: 0;
  height: 20px;
  width: 20px;
  background: $white;
  border-radius: 4px;
  border: 1px solid $dark;
}

/* On mouse-over, add a grey background color */
.container-checkbox:hover input ~ .checkmark {
  background-color: $white;
}

/* When the checkbox is checked, add a blue background */
.container-checkbox input:checked ~ .checkmark {
  background-color: $primary;
  border-color: transparent;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* Show the checkmark when checked */
.container-checkbox input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.container-checkbox .checkmark:after {
  left: 6px;
  top: 2px;
  width: 6px;
  height: 10px;
  border: solid $white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
// custom checkbox style end ---------

// library folder style --------
.library-folder {
  @extend %listSpacing;
  margin: 50px 0 0;
  display: grid;
  grid-template-columns: repeat(6, 1fr); // always 6 columns
  gap: 1rem;

  .folder-card {
    border-radius: 10px;
    border: 2px solid rgba(0, 0, 0, 0.06);
    padding: 10px 15px;
    cursor: pointer;
    text-align: center;

    & > div {
      display: flex;
      justify-content: flex-end;
    }

    button {
      svg {
        transform: rotate(90deg);
        width: 20px;
        opacity: 0.5;
      }
    }

    p {
      font-size: 1.6rem;
      text-align: center;
      font-weight: $semiBold;
      margin-top: 5px;
      margin-bottom: 15px;
    }

    .folder-icon {
      width: 145px;
      height: auto;
      object-fit: contain;
    }
  }

  @media (max-width: 767px) {
    .folder-card {
      .folder-icon {
        width: 110px;
      }
      p {
        font-size: 1.3rem;
      }
    }
  }

  @media (max-width: 400px) {
    .folder-card {
      .folder-icon {
        width: 90px;
      }
      p {
        font-size: 1.1rem;
      }
    }
  }
  // Tablet
  @media (max-width: 992px) {
    grid-template-columns: repeat(3, 1fr); // 3 per row
  }

  // Mobile
  @media (max-width: 600px) {
    grid-template-columns: repeat(2, 1fr); // 2 per row
  }
}

//dot css
.green-dot {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 7px;
    right: -20px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: $green;
  }
}

.red-dot {
  position: relative;
  &::after {
    content: "";
    position: absolute;
    top: 7px;
    right: -20px;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    background: $danger;
  }
}

// Skeleton loading animation
@keyframes skeletonPulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.6;
  }
}

.skeleton-pulse {
  background-color: #e0e0e0;
  animation: skeletonPulse 1.5s ease-in-out infinite;
  border-radius: 4px;
}

// Assessment Summary Swiper style
.fas-swiper-container {
  position: relative;
  margin-bottom: 20px;
  .summary-text-card {
    margin-bottom: 10px;
  }
  .swiper-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .swiper-pagination-text {
      width: auto;
      font-size: $text-md;
      font-weight: $bold;
      .swiper-pagination-current {
        color: $primary;
      }
      .swiper-pagination-total {
        color: $dark;
      }
    }
    .custom-nav-buttons {
      gap: 15px;
      display: flex;
      align-items: center;
      button {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        border: none;
        cursor: pointer;
        // background: $secondary;
        background: linear-gradient(54deg, #74a8ff 20.92%, #aacaff 52.91%, #5d86cc 88.37%);
        // background-clip: text;
        // -webkit-background-clip: text;
        // -webkit-text-fill-color: transparent;
        &.swiper-button-disabled {
          background: rgba($dark, 0.1);
          cursor: not-allowed;
          svg {
            stroke: rgba($dark, 0.5);
          }
        }
        svg {
          stroke: $white;
          width: 16px;
          height: 16px;
        }
        &.rotate {
          transform: rotate(180deg);
        }
      }
    }
  }
}

// interview page style start
.video_call_section {
  border: 1px solid rgba($dark, 0.1);
  border-radius: 20px;
  margin-top: 60px;
  overflow: hidden;

  .interview-content {
    padding: 20px 20px 20px 0;
    .progress-container {
      .time {
        font-size: $text-md;
      }
      .progress-tracker {
        margin-bottom: 50px;
        .bar-container {
          .labels {
            .label {
              margin-top: 10px;
              @media (max-width: 375px) {
                font-size: 0.75rem !important;
              }
            }
          }
        }
      }
    }
    .common-page-head-section {
      .main-heading {
        margin-bottom: 20px;
        h2 {
          font-size: 1.8rem;
        }
      }
      .conductInterview-module-scss-module__yztraq__question_info_box {
        ul {
          margin-bottom: 15px;
          li {
            font-size: $text-sm;
            gap: 10px;
            span {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
      .interview-topic-list {
        .topic-item {
          border-radius: 10px;
          padding: 8px 15px;
          font-size: $text-sm;
          text-wrap: nowrap;
          margin-top: 10px;
          text-align: center;

          .interviewer-name {
            height: 25px;
            width: 25px;
            font-size: 1rem;
            border: 1px solid #fff;
          }
        }
        .swiper {
          width: 100%;
        }
        .swiper-wrapper {
          height: auto;
        }
      }
    }
    .interview-question-cards-height {
      max-height: 512px;
      overflow-y: auto;
      overflow-x: hidden;
      padding-right: 10px;
      padding-bottom: 10px;
      .interview-question-card {
        padding: 15px;
        margin-bottom: 15px;
        .tittle {
          font-size: $text-md;
        }
        h5 {
          font-size: $text-sm;
          font-weight: $medium;
          line-height: 1.4;
        }
      }
    }
    .behavioral-letter-card {
      max-height: 512px;
      min-height: 512px;
      padding: 15px;
      &::after {
        top: -6px;
        width: 30px;
      }
      h5 {
        font-size: 1.8rem;
      }
      .form-group {
        textarea {
          margin-top: 10px;
          height: 100%;
          background: transparent;
          padding: 0;
          border: none;
          font-size: 1.3rem;
          border-radius: 0;
          &:disabled {
            cursor: not-allowed;
            background-color: transparent !important;
          }
        }
      }
    }
    .section-heading {
      h2 {
        font-size: 1.8rem;
        margin-bottom: 10px;
      }
      p {
        font-size: 1.4rem;
      }
    }
    .number-task {
      padding-left: 0;
      margin: 10px 0;
      gap: 8px;
      li {
        font-size: 1.4rem;
        padding: 2px 10px;
        border-radius: 6px;
      }
    }
    @media (max-width: 991px) {
      padding: 20px 0;
      .number-task {
        flex-wrap: wrap;
      }
      .interview-question-cards-height {
        max-height: 100%;
      }
      .behavioral-letter-card {
        max-height: 100%;
        height: 100%;
        min-height: 100%;
      }
    }
  }
  .info-box {
    font-size: $text-sm;
    padding: 10px;
    border-radius: 6px;
    margin-bottom: 20px;
    h5 {
      font-size: $text-sm;
    }
  }
  @media (max-width: 991px) {
    border: none;
    margin-top: 0;
    border-radius: 0;
    .interview-content {
      .progress-container {
        .time {
          font-size: $text-sm;
          margin-bottom: 0;
        }
        .status {
          font-size: 0.9rem;
          min-height: 24px;
          margin-bottom: 0;
        }
        .progress-tracker {
          .bar-container {
            .labels {
              .label {
                font-size: 0.9rem;
              }
            }
          }
        }
      }
    }
  }
}

.sun-editor {
  border-radius: 9px !important;
  .se-toolbar {
    background-color: $primary !important; /* Toolbar background to blue */
    border-bottom: 1px solid $primary !important; /* Slightly darker border for contrast */
    border-radius: 8px !important; /* Add border radius for rounded corners */
  }
  .se-btn-tray button {
    color: $white !important; /* Button icons/text to white for contrast */
    background-color: $primary !important; /* Ensure buttons blend with toolbar */
  }
  .se-btn-tray button:hover {
    background-color: $primary !important; /* Darker blue on hover */
  }
  .se-toolbar-separator-vertical {
    background-color: $white !important; /* Separator lines to white for visibility */
  }
}

// career skills card height styles
.career-based-skills {
  .career-skill-card {
    min-height: 150px;
    @media (max-width: 1400px) and (min-width: 1200px) {
      min-height: 170px;
    }
  }
}
// role skills card height styles
.role-based-skills {
  .career-skill-card {
    min-height: 125px;
  }
}
// culture skills card height styles
.culture-based-skills {
  .career-skill-card {
    min-height: 125px;
  }
}
// calendar styles
.calendar-container {
  position: relative;
  transition: all 0.5s ease-in-out;
  &.side-modal-open {
    .fc-view-harness-active {
      filter: blur(2px);
      transition: all 0.5s ease-in-out;
      cursor: auto;
      a {
        cursor: auto;
        &:hover {
          background: transparent !important;
          color: $primary !important;
          border-color: $primary !important;
          cursor: auto !important;
        }
      }
    }
  }
  .fc-header-toolbar {
    .fc-button-group {
      button {
        border: none;
        border-radius: 16px;
        font-size: 14px;
        border: solid 1px transparent;
        transition: background-color 0.2s ease;
        background-color: $white !important;
        color: rgba($dark, 0.6) !important;
        border-color: rgba($dark, 0.09) !important;
        font-weight: $semiBold;
        text-transform: capitalize;
        // box-shadow:
        //   rgba(50, 50, 105, 0.15) 0px 2px 5px 0px,
        //   rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
        &.fc-button-active {
          background-color: $primary !important;
          color: $white !important;
          border-color: $primary !important;
        }
        &:active {
          background-color: $primary !important;
          color: $white !important;
          border-color: $primary !important;
        }
        &:focus {
          box-shadow: none !important;
        }
        &.active-tab {
          background-color: $dark !important;
          color: $white !important;
          border-color: $dark !important;
        }
      }
    }
    .fc-toolbar-title {
      color: rgba($dark, 0.8) !important;
      font-weight: $bold !important;
      padding: 6px 20px;
      border-radius: 8px;
      font-size: 1.4rem !important;
      border: solid 1px transparent;
      text-transform: capitalize;
      box-shadow:
        rgba(50, 50, 105, 0.15) 0px 2px 5px 0px,
        rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
      @media (max-width: 767px) {
        padding: 0;
        box-shadow: none;
        border: none;
      }
    }
  }
  table {
    thead {
      tr {
        th {
          .fc-col-header-cell-cushion {
            color: rgba($dark, 0.8);
            font-weight: $medium;
            font-size: 1.4rem;
            padding: 15px 0;
          }
        }
      }
    }
    tbody {
      tr {
        td {
          .fc-timegrid-slot-label-frame {
            padding: 15px 0;
            font-size: 1.2rem;
          }
        }
      }
    }
    .fc-day-today {
      background-color: rgba($white, 0.1) !important;
      .fc-daygrid-day-number {
        color: $primary !important;
        font-weight: $bold;
      }
    }
  }
  .fc-dayGridMonth-view,
  .fc-view-harness {
    border: 1.5px solid rgba($dark, 0.2);
    border-radius: 12px;
    box-shadow: 0px 60px 120px 0px rgba(38, 51, 77, 0.05);
    overflow: hidden;
    * {
      a {
        color: $dark;
      }
    }
    table {
      thead {
        tr {
          th {
            .fc-col-header-cell-cushion {
              color: rgba($dark, 0.8);
              font-weight: $medium;
              font-size: 1.4rem;
              padding: 15px 0;
            }
          }
        }
      }
      .fc-day-today {
        background-color: rgba($secondary, 0.1) !important;
        .fc-daygrid-day-number {
          color: $secondary !important;
          font-weight: $extraBold;
        }
      }
    }
    .fc-col-header {
      .fc-col-header-cell-cushion {
        color: rgba($dark, 0.8);
        font-weight: $medium;
        font-size: 1.4rem;
        padding: 15px 0;
      }
    }
    .fc-event {
      flex-direction: column;
      align-items: flex-start;
      gap: 4px;
      background: rgba($primary, 0.1);
      padding: 4px;
      border: solid 1px $primary;
      border-radius: 5px;
      overflow: hidden;
      .fc-event-content {
        display: flex;
        align-items: flex-start;
        flex-direction: row;
        gap: 3px;
        // margin-bottom: 5px;
        .fc-event-time {
          padding: 4px;
          background: $primary;
          display: inline;
          color: $white;
          border-radius: 5px;
          line-height: 1;
          font-size: 8px;
          white-space: nowrap;
          min-width: 32px;
          text-align: center;
        }
        .fc-event-title {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
          line-height: 1.2;
        }
      }
      div {
        font-size: 1.2rem;
        font-weight: $medium;
        color: $dark;
        line-height: 1.5;
        text-wrap: auto;
        // display: inline;
      }

      .fc-daygrid-event-dot {
        border-color: #34a853;
      }
      &.fc-event-future {
        background: rgba($primary, 0.1);
        border-color: $primary;
        .fc-daygrid-event-dot {
          border-color: $primary;
        }
        .fc-event-time {
          background: $primary;
          color: $white;
        }
        &.canceledInterview {
          background: rgba($dark, 0.1);
          border-color: rgba($dark, 0.7);
          border-style: dashed;
          .fc-daygrid-event-dot {
            border-color: $dark;
          }
          .fc-event-time {
            background: rgba($dark, 0.7);
            color: $white;
          }
        }
      }
      &.fc-event-past {
        &.incompleteInterview {
          background: rgba($white, 1);
          border-color: $danger;
          border-style: dashed;
          .fc-daygrid-event-dot {
            border-color: $danger;
          }
          .fc-event-time {
            background: $danger;
            color: $white;
          }
        }
        &.completedInterview {
          background: rgba($white, 1);
          border-color: $primary;
          border-style: dashed;
          .fc-daygrid-event-dot {
            border-color: $primary;
          }
          .fc-event-time {
            background: $primary;
            color: $white;
          }
        }
        &.canceledInterview {
          background: rgba($dark, 0.1);
          border-color: rgba($dark, 0.7);
          border-style: dashed;
          .fc-daygrid-event-dot {
            border-color: $dark;
          }
          .fc-event-time {
            background: rgba($dark, 0.7);
            color: $white;
          }
        }
      }
      &.fc-event-today {
        &.incompleteInterview {
          background: rgba($white, 1);
          border-color: $danger;
          border-style: dashed;
          .fc-daygrid-event-dot {
            border-color: $danger;
          }
          .fc-event-time {
            background: $danger;
            color: $white;
          }
        }
        &.completedInterview {
          background: rgba($white, 1);
          border-color: $primary;
          border-style: dashed;
          .fc-daygrid-event-dot {
            border-color: $primary;
          }
          .fc-event-time {
            background: $primary;
            color: $white;
          }
        }
        &.canceledInterview {
          background: rgba($dark, 0.1);
          border-color: rgba($dark, 0.7);
          border-style: dashed;
          .fc-daygrid-event-dot {
            border-color: $dark;
          }
          .fc-event-time {
            background: rgba($dark, 0.7);
            color: $white;
          }
        }
      }
    }
  }
  .fc-daygrid-more-link {
    font-size: 1.2rem;
    background: transparent;
    padding: 5px 5px;
    border-radius: 5px;
    line-height: 1;
    color: $primary !important;
    width: 100%;
    text-align: center;
    font-weight: $bold;
    border: solid 1px $primary;
    border-radius: 5px;
    text-transform: capitalize;
    &:hover {
      background-color: $dark !important;
      color: $white !important;
      border-color: $dark !important;
    }
  }
  .fc-more-popover {
    display: none;
  }
  .fc-scrollgrid-shrink-cushion {
    font-size: 9px !important;
    font-weight: 800 !important;
    text-transform: capitalize !important;
  }
}
.calendar-color-indication {
  display: flex;
  gap: 4rem;
  align-items: center;
  @extend %listSpacing;
  margin-bottom: 0;
  background: white;
  z-index: 100;
  position: relative;
  padding: 10px 0;
  li {
    font-size: $text-sm;
    font-weight: $medium;
    color: $dark;
    display: flex;
    align-items: center;
    gap: 1rem;
    span {
      width: 20px;
      height: 20px;
      border-radius: 100%;
      display: block;
      &.upcoming {
        background: rgba($primary, 0.1);
        border: solid 1px $primary;
      }
      &.incompleteInterview {
        background: $white;
        border: dashed 1px $danger;
      }
      &.completedInterview {
        background: $white;
        border: dashed 1px $primary;
      }
      &.canceledInterview {
        background: rgba($dark, 0.1);
        border: dashed 1px rgba($dark, 0.7);
      }
    }
  }
}

/* HTML: <div class="loader"></div> */
.loader-recording {
  width: 15px;
  aspect-ratio: 1;
  border-radius: 50%;
  animation: l5 1s infinite linear alternate;
}
@keyframes l5 {
  0% {
    box-shadow:
      20px 0 #000,
      -20px 0 #0002;
    background: #000;
  }
  33% {
    box-shadow:
      20px 0 #000,
      -20px 0 #0002;
    background: #0002;
  }
  66% {
    box-shadow:
      20px 0 #0002,
      -20px 0 #000;
    background: #0002;
  }
  100% {
    box-shadow:
      20px 0 #0002,
      -20px 0 #000;
    background: #000;
  }
}

/* HTML: <div class="loader"></div> */
.loader-recording {
  width: 15px;
  aspect-ratio: 1;
  border-radius: 50%;
  animation: l5 1s infinite linear alternate;
}
@keyframes l5 {
  0% {
    box-shadow:
      20px 0 #000,
      -20px 0 #0002;
    background: #000;
  }
  33% {
    box-shadow:
      20px 0 #000,
      -20px 0 #0002;
    background: #0002;
  }
  66% {
    box-shadow:
      20px 0 #0002,
      -20px 0 #000;
    background: #0002;
  }
  100% {
    box-shadow:
      20px 0 #0002,
      -20px 0 #000;
    background: #000;
  }
}
// give to ai css
.header_links {
  @extend %listSpacing;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  background: #ecf1f8;
  padding: 10px;
  border-radius: 10px;
  position: relative;
  z-index: 1;
  @media (max-width: 991px) {
    display: none;
  }
  li {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.4rem;
    font-weight: $medium;
    cursor: pointer;
    text-transform: capitalize;
    padding: 5px 10px;
    border-radius: 10px;
    line-height: 1;
    color: $dark;
    svg {
      width: 20px;
      height: 21px;
      fill: $dark;
    }
    &.active {
      background: transparent;
      color: $white;
      transition: 0.5s;
      font-weight: $bold;
      svg {
        fill: $white;
      }
    }
    &:hover {
      transition: 0.5s;
      &:not(.active) {
        background: $primary;
        color: $white;
        border-color: $white;
        svg {
          fill: $white;
        }
      }
    }
  }
  span {
    position: absolute;
    top: 0;
    left: 9px;
    bottom: 0;
    width: 96px;
    height: 37px;
    border-radius: 8px;
    transition: 0.5s;
    z-index: -1;
    margin: auto;
  }

  li:nth-child(1).active ~ span {
    left: 9px;
    width: 132px;
    background: $primary;
  }
  li:nth-child(2).active ~ span {
    left: 146px;
    width: 132px;
    background: $primary;
  }

  li:nth-child(3).active ~ span {
    left: 280px;
    width: 118px;
    background: $primary;
  }

  li:nth-child(4).active ~ span {
    left: 404px;
    width: 85px;
    background: $primary;
  }
}
.subscription-successful-page {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 160px);
  padding: 40px 20px;
  background: linear-gradient(180deg, rgba($primary, 0.03) 0%, rgba($primary, 0.01) 100%);

  .subscription-successful {
    width: 100%;
    max-width: 560px;
    background: $white;
    border-radius: 24px;
    padding: 48px 40px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(#000, 0.06);
    animation: fade-in 0.6s ease;
  }

  .success-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 24px;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  .subscription-successful-title {
    font-size: 2.4rem;
    font-weight: $bold;
    color: $primary;
    margin-bottom: 16px;
  }

  .subscription-details {
    margin-bottom: 32px;
    p {
      font-size: $text-md;
      color: rgba($dark, 0.8);
      font-weight: $medium;
    }
    .subscription-card {
      background: rgba($primary, 0.05);
      border: 1px solid rgba($primary, 0.15);
      border-radius: 16px;
      padding: 24px 20px;
      text-align: left;

      h3 {
        font-size: 1.6rem;
        font-weight: $semiBold;
        margin-bottom: 12px;
        color: $dark;
      }

      .subscription-success-title {
        font-size: 1.8rem;
        font-weight: $bold;
        color: $primary;
        margin-bottom: 12px;
      }

      .subscription-success-info {
        display: grid;
        row-gap: 8px;
        font-size: $text-sm;
        font-weight: $medium;

        div {
          display: flex;
          justify-content: space-between;

          strong {
            font-weight: $medium;
            color: $dark;
          }
        }
      }
    }
  }

  .subscription-successful-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 32px;
    .primary-btn,
    .dark-outline-btn {
      min-width: 300px;
    }

    @media (min-width: 576px) {
      flex-direction: row;
      justify-content: center;

      .primary-btn,
      .dark-outline-btn {
        min-width: 180px;
      }
    }
  }

  @keyframes fade-in {
    from {
      opacity: 0;
      transform: translateY(12px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}


// .sidebar {
//   position: fixed;
//   top: 0;
//   right: -260px; /* start hidden on the right */
//   width: 260px;
//   height: 100%;
//   background-color: #fff;
//   box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
//   padding: 20px;
//   transition: right 0.5s ease; /* animation */
//   z-index: 1000;
// }

// .sidebar.open {
//   right: 0; /* slide in from right */
// }

// .sidebar-header {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
// }

// .sidebar-menu {
//   list-style: none;
//   padding: 0;
//   margin-top: 20px;
// }

// .sidebar-menu li {
//   padding: 12px 0;
//   border-bottom: 1px solid #eee;
//   cursor: pointer;
// }

// .close-btn {
//   font-size: 24px;
//   background: none;
//   border: none;
//   cursor: pointer;
// }

// .overlay {
//   position: fixed;
//   top: 0;
//   left: 0;
//   width: 100%;
//   height: 100%;
//   background-color: rgba(0, 0, 0, 0.3);
//   z-index: 999;
// }

@media (min-width: 992px) {
  .hamburger,
  .mobile-sidebar {
    display: none;
  }
}

.sidebar {
  position: fixed;
  top: 0;
  right: 0;
  width: 280px;
  height: 100%;
  background-color: #fff;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transform: translateX(100%);
  transition: transform 0.5s ease;
  z-index: 1000;
}

.sidebar.open {
  transform: translateX(0);
}

.sidebar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .circle_img {
    display: flex;
    align-items: center;
    gap: 10px;
    h5 {
      font-size: $text-md;
      font-weight: $medium;
      margin-bottom: 2px;
    }
    span {
      font-size: $text-xs;
      font-weight: $regular;
    }
  }
  svg {
    width: 24px;
    height: 24px;
    fill: $dark;
  }
  img {
    width: 40px;
    height: 40px;
    min-width: 40px;
    border-radius: 100px;
    overflow: hidden;
    object-fit: cover;
    border: solid 1px rgba($dark, 0.1);
  }
}

.sidebar-menu {
  list-style: none;
  padding: 0;
  margin-top: 20px;
  &.sidebar-sub-menu {
    padding-left: 25px;
    margin: 0;
    li {
      font-size: 1.3rem;
      color: rgba($dark, 0.8);
      padding: 10px 0;
    }
  }
  li {
    padding: 12px 0;
    border-bottom: 1px dashed rgba($dark, 0.2);
    cursor: pointer;
    font-size: 1.4rem;
    font-weight: $regular;
    display: flex;
    align-items: center;
    gap: 10px;
    &:last-child {
      border-bottom: none;
      padding-bottom: 0;
    }
    &.sub-menu-bar {
      flex-direction: column;
      align-items: flex-start;
      gap: 0;
      .sub-menu-list {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
    svg {
      width: 18px !important;
      height: 18px !important;
      fill: $dark;
      &.strokeSvg {
        fill: none;
        stroke: $dark;
      }
    }
    &:active {
      color: $primary;
      svg {
        fill: $primary;
      }
    }
  }
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.user-profile-btn {
  display: flex;
  gap: 20px;
  align-items: center;
  position: relative;
  // &::after {
  //   content: "";
  //   width: 1px;
  //   height: 100%;
  //   background: #3333;
  //   position: absolute;
  //   right: 128px;
  //   top: 0;
  //   bottom: 0;
  //   margin: auto;
  // }
}

// performance-based-skill page css
.performance-based-skill {
  .big-card-hight {
    max-height: 780px;
    min-height: 780px;
    overflow: auto;
    padding-right: 10px;
  }
  .small-card-hight {
    max-height: 350px;
    min-height: 350px;
    overflow: auto;
    padding-right: 10px;
    .drag-skill-card {
      background: linear-gradient(54deg, rgba(116, 168, 255, 0.3) 20.92%, rgba(170, 202, 255, 0.3) 52.91%, rgba(93, 134, 204, 0.3) 88.37%) !important;
    }
  }

  .drag-card {
    border: solid 1px rgba($dark, 0.2);
    border-radius: 16px;
    padding: 15px 20px;
    padding-right: 10px;
    margin-right: 10px;
    .tittle {
      font-size: $text-md;
      font-weight: $bold;
      color: rgba($dark, 1);
      margin-bottom: 10px;
      .badge {
        padding: 3px 5px;
        font-size: 12px !important;
        margin-left: 5px;
        background: rgba($primary, 0.1);
      }
    }
    select {
      padding: 0 10px;
      min-width: 50px;
      border: none;
      outline: none;
      box-shadow: none;
      text-transform: capitalize;
      color: $primary;
      option {
        padding: 0;
        width: 100%;
      }
    }
  }
  .drag-drop-icon {
    .drag-drop-text {
      font-size: $text-sm;
      font-weight: $medium;
      color: rgba($dark, 1);
      text-align: center;
      margin-bottom: 5px;
    }
  }
  .drag-here {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 100%;
    width: 100%;
    p {
      font-size: 1.2rem;
      font-weight: $regular;
      color: rgba($dark, 0.6);
      text-align: center;
      padding: 0;
      margin-top: -20px;
    }
  }

  .drag-ghost {
    position: fixed !important;
    pointer-events: none !important;
    z-index: 1000 !important;
    opacity: 0.8 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
  }

  .dragging-original {
    opacity: 0.3 !important;
  }

  .skill-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  .skill-card {
    transition: all 0.2s ease;
  }
}
.min-table {
  min-height: 400px;
  // background-color: #dee9ff;
}

.info-align {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 5px;
}
/* Hide on tablet & mobile (below 1024px) */
@media (max-width: 991px) {
  .not-show-tab-mobile {
    display: none !important;
  }
}

/* Hide after tablet (above 1024px, i.e. desktop and larger) */
@media (min-width: 992px) {
  .not-show-after-tab {
    display: none !important;
  }
}

// loader styles
.loading-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  animation: fadeIn 1.5s ease-in-out infinite alternate;
  .loading-text {
    font-size: 1.8rem;
    font-weight: $medium;
    color: $primary;
    margin-bottom: 1rem;
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.attachment-item {
  a {
    font-size: 1.6rem;
    img {
      display: none;
    }
  }
}
.anchore-img-not {
  img {
    display: none;
  }
}
// ============================================
// NO INTERNET PAGE STYLES
// ============================================
.no-internet-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
  font-family: "Plus Jakarta Sans", sans-serif;

  .no-internet-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    z-index: 2;
  }

  .no-internet-content {
    position: relative;
    max-width: 600px;
    width: 100%;
  }

  // Animated Background Elements
  .bg-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;

    .floating-circle {
      position: absolute;
      border-radius: 50%;
      opacity: 0.1;
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 120px;
        height: 120px;
        background: $primary;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 80px;
        height: 80px;
        background: $secondary;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 150px;
        height: 150px;
        background: $primary;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }

  // Main Content
  .main-content {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 24px;
    padding: 3rem;
    padding-top: 4rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 3;
  }

  // Image Container
  .image-container {
    position: relative;
    display: inline-block;
    margin-bottom: 2rem;

    .no-internet-image {
      position: relative;
      z-index: 2;
      filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
      animation: bounce 3s ease-in-out infinite;
    }

    .pulse-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 300px;
      height: 300px;
      border: 3px solid $primary;
      border-radius: 50%;
      opacity: 0.3;
      animation: pulse 2s ease-in-out infinite;
    }
  }

  // Text Content
  .text-content {
    .title {
      font-size: 2.5rem;
      font-weight: 700;
      color: $dark;
      margin-bottom: 1rem;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .description {
      font-size: 1.2rem;
      color: $grey;
      margin-bottom: 2rem;
      line-height: 1.6;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;

      @media (max-width: 768px) {
        font-size: 1.1rem;
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    .no-internet-container {
      padding: 1rem;
    }

    .main-content {
      padding: 2rem 1.5rem;
    }

    .action-buttons {
      flex-direction: column;
      align-items: center;

      .primary-btn,
      .secondary-btn {
        width: 100%;
        max-width: 200px;
      }
    }
  }
}

// ============================================
// ANIMATIONS
// ============================================
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.1;
  }
  100% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 0.3;
  }
}
/* Wrapper for alignment */
.custom-radio-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
  /* Hide default radio look */
  .custom-radio-input {
    -webkit-appearance: none;
    appearance: none;
    margin: 0;
    width: 22px;
    min-width: none;
    width: 22px;
    height: 22px;
    border: 1px solid #4064b1; /* blue border */
    border-radius: 50%;
    display: grid;
    place-content: center;
    cursor: pointer;
    background-color: #fff;
    transition:
      border-color 0.2s ease-in-out,
      background-color 0.2s ease-in-out;
  }

  /* Inner dot */
  .custom-radio-input::before {
    content: "";
    width: 12px;
    height: 12px;
    border-radius: 50%;
    transform: scale(0);
    background-color: #4064b1; /* blue fill */
    transition: transform 0.2s ease-in-out;
  }

  /* Checked state */
  .custom-radio-input:checked::before {
    transform: scale(1);
  }

  /* Label styling */
  .custom-radio-label {
    cursor: pointer;
    font-size: $text-xl;
    font-weight: $bold;
  }
}
