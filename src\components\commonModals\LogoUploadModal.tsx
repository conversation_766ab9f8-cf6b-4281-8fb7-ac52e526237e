// src/components/commonModals/CalenderEventModal.tsx
"use client";
import React, { FC, useEffect, useRef, useState } from "react";
import "../../styles/eventModal.scss";
import ModalCloseIcon from "../svgComponents/ModalCloseIcon";
import Button from "../formElements/Button";
import InputWrapper from "../formElements/InputWrapper";
import UploadBox from "../commonComponent/UploadBox";
import Image from "next/image";
import uploadedLogo from "../../../public/assets/images/logo.svg";

import { useDispatch, useSelector } from "react-redux";
import { selectProfileData, updateUserProfileData } from "@/redux/slices/authSlice";
import { useTranslate } from "@/utils/translationUtils";
import { IMAGE_EXTENSIONS, inputLogoType, MAX_IMAGE_SIZE, S3_PATHS } from "@/constants/commonConstants";
import { toastMessageError, toastMessageSuccess, uploadFileOnS3 } from "@/utils/helper";
import { updateLogo } from "@/services/logoService";
import Loader from "../loader/Loader";
import { removeAttachmentsFromS3 } from "@/services/commonService";

// Note: All form logic/components have been removed for static UI development.

interface EditProfileModalProps {
  onClickCancel: () => void;
  onSubmitSuccess: () => void;
}

const LogoUploadModal: FC<EditProfileModalProps> = ({ onClickCancel, onSubmitSuccess }) => {
  const userProfile = useSelector(selectProfileData);

  console.log("User-----Profil----", userProfile);

  const t = useTranslate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();

  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(userProfile?.logo || null);
  const [isLoading, setIsLoading] = useState(false);
  const [formChanged, setFormChanged] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  // Update form values when logoUrl changes
  useEffect(() => {
    if (imagePreview) {
      setImagePreview(imagePreview);
    }
  }, [userProfile]);

  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setIsLoading(true);
    setFormChanged(true); // Mark form as changed when image is updated
    const { files } = e.target;

    if (files?.length && Number(files?.[0].size) < MAX_IMAGE_SIZE) {
      const file = files[0];

      if (file) {
        const extension = file?.type?.split("/")[1];

        if (IMAGE_EXTENSIONS.includes(extension?.toLowerCase())) {
          const objectUrl = URL.createObjectURL(file);
          console.log("objectUrl--------", objectUrl);

          setImagePreview(objectUrl);

          setImageFile(file);

          // If there's an existing image, we'll delete it when submitting the form
        } else {
          toastMessageError(t("invalid_file_format"));
        }
      }
    } else {
      toastMessageError(t("invalid_image_size_format"));
    }

    // Reset the input field
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    setIsLoading(false);
  };

  const uploadImage = async (file: File): Promise<string> => {
    try {
      const filePath = S3_PATHS.UPLOAD_LOGO.replace(":orgId", `${userProfile?.orgId}`); // e.g., organization-logos/12345

      // Upload the file to S3
      const uploadedFileUrl = (await uploadFileOnS3(file, filePath)) as string;
      console.log("Image Uploaded file URL:", uploadedFileUrl);

      return uploadedFileUrl;
    } catch (error) {
      console.error(t("error_uploading_image"), error);
      throw new Error(t("failed_to_upload_image"));
    }
  };

  const onSubmit = async () => {
    if (!formChanged) return; // Prevent submit if nothing changed
    try {
      setIsSubmitting(true);

      let imageUrl = userProfile?.logo || null;

      // If a new image is selected
      if (imageFile) {
        // Upload the new image

        const result = await removeAttachmentsFromS3({ fileUrlArray: JSON.stringify([imageUrl]) });
        imageUrl = await uploadImage(imageFile);
        console.log("Removed old image:", result);
        console.log("Image uploaded successfully:", JSON.stringify(imageUrl));

        // Call API to update profile
        const response = await updateLogo(imageUrl || "");

        console.log("Profile updated --API call");
        if (response.data && response.data.success) {
          console.log("Profile updated successfully:", response);

          dispatch(updateUserProfileData({ logo: imageUrl }));
          toastMessageSuccess(t(response.data.message));
          onSubmitSuccess(); // Close modal after success
        } else {
          const errorMessage = t(response.data?.message || "failed_to_update_profile");
          toastMessageError(errorMessage);
        }
      } else {
        toastMessageError(t("something_went_wrong"));
      }
    } catch (error) {
      console.error(t("error_updating_profile"), error);
      const errorMessage = t("an_error_occurred_while_updating_profile");
      toastMessageError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
    console.log("User-----Profil---11111-", userProfile?.logo);
  };

  // Static modal for UI development
  return (
    <>
      <div className="modal theme-modal show-modal">
        <div className="modal-dialog modal-dialog-centered">
          <div className="modal-content">
            <div className="modal-header pb-0">
              <h2 className="mt-0 mb-4">{t("upload_organization_logo")}</h2>
              <button className="modal-close-btn" type="button" disabled={isLoading || isSubmitting} onClick={onClickCancel}>
                <ModalCloseIcon />
              </button>
            </div>
            <div className="modal-body">
              <form>
                <InputWrapper>
                  {/* uploaded-item */}
                  <div className="d-flex flex-wrap">
                    <InputWrapper.Label htmlFor="attachments">{t("logo_preview")}</InputWrapper.Label>
                    <div className="uploaded-image">
                      {userProfile?.logo ? (
                        <Image src={imagePreview ? imagePreview : userProfile.logo} alt="uploaded logo" width={100} height={100} />
                      ) : (
                        <Image src={uploadedLogo} alt="uploaded logo" width={100} height={100} />
                      )}
                    </div>
                  </div>

                  <UploadBox
                    UploadBoxClassName="upload-card-sm"
                    onChange={handleImageChange}
                    disabled={isSubmitting}
                    isLoading={isSubmitting}
                    type={inputLogoType}
                  />
                </InputWrapper>
                <div className="row mt-4">
                  <div className="col-md-6">
                    {/* <Button type="submit" className="primary-btn w-100 rounded-md">
                      {"Save"}
                    </Button> */}
                    <Button
                      className="primary-btn rounded-md w-100"
                      onClick={onSubmit}
                      disabled={isSubmitting || !formChanged}
                      title={!formChanged ? t("no_changes_made") : ""}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader className="spinner-border-sm me-2" />
                          {t("saving_changes")}
                        </>
                      ) : (
                        t("save_changes")
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default LogoUploadModal;
