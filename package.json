{"name": "stratum9-interview", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "lint": "eslint src && tsc && prettier src --check", "test": "playwright test", "build": "next build", "start": "next start", "prepare": "husky install", "prettier-check": "prettier --check .", "prettier-fix": "prettier --write .", "lint-check": "eslint src/**/*.{ts,tsx} --report-unused-disable-directives --max-warnings 100", "lint-fix": "eslint --fix src/**/*.{ts,tsx}", "type-check": "tsc --noEmit"}, "lint-staged": {"src/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"], "*.{js,mjs,json,css,scss,md}": ["prettier --write"]}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@hookform/resolvers": "^5.0.1", "@react-spring/web": "^10.0.2", "@reduxjs/toolkit": "^2.8.2", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@types/file-saver": "^2.0.7", "@types/html2canvas": "^0.5.35", "@types/js-cookie": "^3.0.6", "agora-rtc-react": "^2.4.0", "agora-rtc-sdk-ng": "^4.23.4", "axios": "^1.9.0", "bootstrap": "^5.3.6", "chart.js": "^4.5.0", "ckeditor5": "^45.1.0", "core-js": "^3.42.0", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "fullcalendar": "^6.1.17", "html2canvas": "^1.4.1", "html2pdf.js": "^0.10.3", "js-cookie": "^3.0.5", "jspdf": "^2.5.2", "jspdf-autotable": "^5.0.2", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "next": "^15.3.0", "next-auth": "^4.24.11", "next-intl": "^4.0.0", "react": "^19.0.0", "react-avatar": "^5.0.4", "react-chartjs-2": "^5.3.0", "react-circular-progressbar": "^2.2.0", "react-datepicker": "^8.4.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.3", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.16.0", "react-joyride-react-19": "^2.9.2", "react-jwt": "^1.3.0", "react-loading-skeleton": "^3.5.0", "react-otp-input": "^3.1.1", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-time-picker": "^7.0.0", "react-tooltip": "^5.28.1", "redux-persist": "^6.0.0", "sass": "^1.88.0", "secure-ls": "^2.0.0", "sharp": "^0.34.3", "socket.io-client": "^4.8.1", "suneditor": "^2.47.5", "suneditor-react": "^3.6.1", "swiper": "^11.2.8", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3.1.0", "@playwright/test": "^1.51.0", "@types/crypto-js": "^4.1.1", "@types/lodash": "^4.17.5", "@types/node": "^20.14.5", "@types/react": "^19.0.0", "@types/react-dom": "^19.1.5", "@types/react-infinite-scroll-component": "^4.2.5", "@types/tinymce": "^4.6.9", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "9.11.1", "eslint-config-next": "^15.3.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "husky": "^8.0.0", "lint-staged": "^16.0.0", "prettier": "^3.3.3", "typescript": "^5"}}