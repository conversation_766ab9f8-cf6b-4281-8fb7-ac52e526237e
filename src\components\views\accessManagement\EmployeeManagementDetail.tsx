"use client";
import React, { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import styles from "../../../styles/accessManagement.module.scss";
import Button from "@/components/formElements/Button";
import InputWrapper from "@/components/formElements/InputWrapper";
import Textbox from "@/components/formElements/Textbox";
import SearchIcon from "@/components/svgComponents/SearchIcon";
import { useForm } from "react-hook-form";
import ThreeDotsIcon from "@/components/svgComponents/ThreeDotsIcon";
import { useRouter } from "next/navigation";
import { getEmployeesByDepartment, updateEmployeeRole, updateEmployeeInterviewOrder, updateEmployeeStatus } from "@/services/employeeService";
import { EmployeeInterface, Role } from "@/interfaces/employeeInterface";
import ChangeRoleModal from "@/components/commonModals/ChangeRoleModal";
import UpdateInterviewOrderModal from "@/components/commonModals/UpdateInterviewOrderModal";
import RemoveEmployeeModal from "@/components/commonModals/RemoveEmployeeModal";
import { toastMessageError, toastMessageSuccess, capitalizeFirstChar, getDecryptedData, toTitleCase } from "@/utils/helper";
import ROUTES from "@/constants/routes";
import InfiniteScroll from "react-infinite-scroll-component";
import { findRole } from "@/services/roleService";
import { debounce } from "lodash";
import { STANDARD_LIMIT, TYPING_PREVENTION_CHARACTERS } from "@/constants/commonConstants";
import TableSkeleton from "../skeletons/TableSkeleton";
import ConfirmationModal from "@/components/commonModals/ConfirmationModal";
import { useTranslate } from "@/utils/translationUtils";

// Interface for role change modal state
interface RoleChangeModalState {
  show: boolean;
  employeeId: number | null;
  employeeName: string;
  currentRole: string;
  newRoleId: number | null;
  newRoleName: string;
}

// Interface for interview order modal state
interface InterviewOrderModalState {
  show: boolean;
  employeeId: number | null;
  employeeName: string;
  currentOrder: number;
}

// Interface for remove employee modal state
interface RemoveEmployeeModalState {
  show: boolean;
  employee: EmployeeInterface | null;
}

const EmployeeManagementDetail = () => {
  const t = useTranslations();
  const translate = useTranslate();
  const router = useRouter();
  const { control } = useForm<{ search: string }>({ defaultValues: { search: "" } });
  const [departmentId, setDepartmentId] = useState<number | null>(null);
  const [departmentName, setDepartmentName] = useState<string>("");
  const [employees, setEmployees] = useState<EmployeeInterface[]>([]);
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [statusLoading, setStatusLoading] = useState(false);
  const [offset, setOffset] = useState(0);
  const [searchStr, setSearchStr] = useState("");
  const [hasMore, setHasMore] = useState(true);
  const [orderUpdated, setOrderUpdated] = useState(false);
  const [openActionId, setOpenActionId] = useState<number | null>(null);
  const [updatingRoleId, setUpdatingRoleId] = useState<number | null>(null);
  const [updatingOrderId, setUpdatingOrderId] = useState<number | null>(null);
  const [encryptedDeptId, setEncryptedDeptId] = useState<string>("");
  const [roleChangeModal, setRoleChangeModal] = useState<RoleChangeModalState>({
    show: false,
    employeeId: null,
    employeeName: "",
    currentRole: "",
    newRoleId: null,
    newRoleName: "",
  });
  const [orderModal, setOrderModal] = useState<InterviewOrderModalState>({
    show: false,
    employeeId: null,
    employeeName: "",
    currentOrder: 0,
  });
  const [removeEmployeeModal, setRemoveEmployeeModal] = useState<RemoveEmployeeModalState>({
    show: false,
    employee: null,
  });

  const [statusConfirmModal, setStatusConfirmModal] = useState<{
    show: boolean;
    employeeId: number | null;
    status: boolean;
  }>({
    show: false,
    employeeId: null,
    status: false,
  });

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (TYPING_PREVENTION_CHARACTERS.includes(e.key)) {
      e.preventDefault();
    }
  };

  // Fetch roles when component mounts or departmentId changes
  useEffect(() => {
    const fetchRoles = async () => {
      try {
        const rolesResponse = await findRole();
        if (rolesResponse.data?.data) {
          setRoles(rolesResponse.data.data);
        }
      } catch (error) {
        console.error("Error fetching roles:", error);
      }
    };

    if (departmentId) {
      fetchRoles();
    }
  }, [departmentId]);

  // Fetch employees when search value changes or department changes
  useEffect(() => {
    if (departmentId) {
      fetchMoreEmployees("", 0, true);
    }
  }, [departmentId]);

  useEffect(() => {
    const searchParams = new URLSearchParams(window.location.search);
    const encryptedId = searchParams.get("departmentId");
    const name = searchParams.get("departmentName");

    if (encryptedId) {
      // Store the original encrypted ID for navigation to other pages
      setEncryptedDeptId(encryptedId);

      try {
        // Decrypt the ID parameter
        const decryptedData = getDecryptedData(encryptedId);
        // Extract departmentId from the decrypted object and ensure it's a number
        const id = decryptedData?.departmentId ? Number(decryptedData.departmentId) : NaN;

        if (isNaN(id)) {
          console.error("Invalid department ID");
          toastMessageError(t("invalid_or_missing_department_id"));
          router.back();
          return;
        }

        setDepartmentId(id);
      } catch (error) {
        console.error("Error decrypting department ID:", error);
        toastMessageError(t("something_went_wrong"));
        router.back();
      }
    }

    if (name) {
      setDepartmentName(decodeURIComponent(name));
    }
  }, [router, t]);

  // Handle orderUpdated state reset separately
  useEffect(() => {
    if (orderUpdated) {
      setOrderUpdated(false);
    }
  }, [orderUpdated]);

  // Close action dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Check if the click was outside any action dropdown
      const target = event.target as HTMLElement;
      if (openActionId !== null && !target.closest(".position-relative")) {
        setOpenActionId(null);
      }
    };

    // Add event listener
    document.addEventListener("mousedown", handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openActionId]);

  const fetchMoreEmployees = async (searchValue: string, currentOffset = 0, reset = false) => {
    setLoading(true);
    if (reset) {
      setEmployees([]);
      setOffset(0);
      setHasMore(true); // assume more until API tells otherwise
    }
    console.log("loading stateeeee111111======ENter for searxch==>", loading);

    try {
      const result = await getEmployeesByDepartment(
        {
          limit: STANDARD_LIMIT,
          offset: currentOffset,
          search: searchValue,
        },
        +departmentId!
      );
      console.log("loading stateeeee222222========>", loading);

      if (result?.data?.success) {
        const employeesFetched = result.data.data;

        setEmployees((prevEmployees) => (reset ? employeesFetched : [...prevEmployees, ...employeesFetched]));

        if (employeesFetched.length < STANDARD_LIMIT) {
          setHasMore(false);
        } else {
          setHasMore(true);
        }

        setOffset(currentOffset + employeesFetched.length);
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.log("loading stateeeee333344444443========>", loading);
      console.error(t("error_fetching_employees"), error);
      setHasMore(false);
    }
    //  finally {

    //   console.log("loading stateeeee3333========>", loading);
    // }
    setLoading(false);
  };

  const handleSearchInputChange = (event: string) => {
    console.log("enter in search", event);

    setLoading(true);
    const searchString = event.trim();
    setSearchStr(searchString);
    fetchMoreEmployees(searchString, 0, true);
  };

  const debouncedHandleSearchInputChange = debounce(handleSearchInputChange, 500);

  // Function to prepare role change confirmation modal
  const prepareRoleChange = (employeeId: number, roleId: number) => {
    const employee = employees.find((emp) => emp.id === employeeId);
    if (!employee) return;

    const newRole = roles.find((role) => role.id === roleId);
    if (!newRole) return;

    // Don't show modal if selecting the same role
    if (employee.selectedRole.id === roleId) return;

    const employeeName = `${employee.firstName} ${employee.lastName}`.trim() || `User ID: ${employee.userId}`;

    setRoleChangeModal({
      show: true,
      employeeId,
      employeeName,
      currentRole: employee.selectedRole.name,
      newRoleId: roleId,
      newRoleName: newRole.name,
    });
  };

  // Function to handle role change after confirmation
  const handleRoleChange = async () => {
    if (!roleChangeModal.employeeId || !roleChangeModal.newRoleId) return;

    try {
      setUpdatingRoleId(roleChangeModal.employeeId);

      const response = await updateEmployeeRole(roleChangeModal.employeeId, roleChangeModal.newRoleId);

      if (response.data?.success) {
        // Update the local state to reflect the change
        toastMessageSuccess(t(response?.data?.message ? response.data.message : "role_updated_success"));
        setEmployees((prevEmployees) =>
          prevEmployees.map((employee) => {
            if (employee.id === roleChangeModal.employeeId) {
              // Find the selected role from allRoles
              const newSelectedRole = roles.find((role) => role.id === roleChangeModal.newRoleId);

              // Update the employee's selected role and allRoles
              return {
                ...employee,
                selectedRole: newSelectedRole || employee.selectedRole,
                allRoles: roles.map((role) => ({
                  ...role,
                  selected: role.id === roleChangeModal.newRoleId,
                })),
              };
            }
            return employee;
          })
        );
      } else {
        toastMessageError(t(response?.data?.message ? response.data.message : "failed_update_role"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("error_updating_role"));
    } finally {
      setUpdatingRoleId(null);
      // Close the modal after update
      setRoleChangeModal((prev) => ({ ...prev, show: false }));
    }
  };

  // Function to cancel role change
  const cancelRoleChange = () => {
    setRoleChangeModal((prev) => ({ ...prev, show: false }));
  };

  // Function to prepare interview order update modal
  const prepareOrderChange = (employee: EmployeeInterface) => {
    setOrderModal({
      show: true,
      employeeId: employee.id,
      employeeName: `${employee.firstName || ""} ${employee.lastName || ""}`.trim(),
      currentOrder: employee.interviewOrder || 0,
    });
  };

  // Function to handle interview order update
  const handleOrderChange = async (newOrder: number) => {
    if (!orderModal.employeeId) return;

    console.log("newOrder=======>", newOrder);
    try {
      setUpdatingOrderId(orderModal.employeeId);
      const response = await updateEmployeeInterviewOrder(orderModal.employeeId, newOrder, departmentId ?? 0);

      if (response.data?.success && response.data.data) {
        // Update all employees' interview orders based on the API response
        // if (response.data.data) {
        //   setEmployees((prevEmployees) => {
        //     // Create a map of id to new interview order from the API response
        //     const orderUpdates = new Map(response.data.data.map((item) => [item.id, item.interviewOrder]));

        //     // Update all employees with their new interview orders
        //     return prevEmployees.map((emp) => {
        //       const newInterviewOrder = orderUpdates.get(emp.id);
        //       return newInterviewOrder !== undefined ? { ...emp, interviewOrder: newInterviewOrder } : emp;
        //     });
        //   });
        // }

        fetchMoreEmployees("", 0, true);
        // Set flag to indicate order has been updated
        setOrderUpdated(true);
        toastMessageSuccess(translate(response?.data?.message ? response.data.message : "employee_interview_order_updated"));
      } else {
        toastMessageError(translate(response?.data?.message ? response.data.message : "failed_update_interview_order"));
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("some_error_occurred"));
    } finally {
      setUpdatingOrderId(null);
      cancelOrderChange();
    }
  };

  // Function to cancel interview order update
  const cancelOrderChange = () => {
    setOrderModal({
      show: false,
      employeeId: null,
      employeeName: "",
      currentOrder: 0,
    });
  };

  // Function to prepare employee removal modal
  // const prepareEmployeeRemoval = (employee: EmployeeInterface) => {
  //   setRemoveEmployeeModal({
  //     show: true,
  //     employee: employee,
  //   });
  // };

  // Function to handle employee removal
  const handleEmployeeRemoval = async (message: string) => {
    // The actual deletion is handled in the modal
    // Here we just update the local state to remove the employee from the list
    if (removeEmployeeModal.employee) {
      setEmployees((prevEmployees) => prevEmployees.filter((emp) => emp.id !== removeEmployeeModal.employee?.id));
      toastMessageSuccess(message);
    }
  };

  // Function to cancel employee removal
  const cancelEmployeeRemoval = () => {
    setRemoveEmployeeModal({
      show: false,
      employee: null,
    });
  };

  // Toggle action dropdown
  const toggleActionDropdown = (employeeId: number) => {
    setOpenActionId((prev) => (prev === employeeId ? null : employeeId));
  };

  const UpdateEmployeeStatus = async () => {
    setStatusLoading(true);
    try {
      if (statusConfirmModal.employeeId !== null) {
        const response = await updateEmployeeStatus(statusConfirmModal.employeeId, statusConfirmModal.status);
        if (response.data?.success) {
          setStatusConfirmModal({ show: false, employeeId: null, status: false });
          // Update local employee state
          setEmployees((prevEmployees) =>
            prevEmployees.map((emp) => (emp.id === statusConfirmModal.employeeId ? { ...emp, isActive: statusConfirmModal.status } : emp))
          );
          setStatusLoading(false);
          toastMessageSuccess(t(response?.data?.message));
        } else {
          toastMessageError(t(response?.data?.message ? response.data.message : "failed_update_status"));
          setStatusLoading(false);
        }
      }
    } catch (error) {
      console.error(error);
      toastMessageError(t("error_updating_status"));
    } finally {
      setUpdatingRoleId(null);
    }
    setStatusLoading(false);
  };

  return (
    <>
      <section className={styles.access_management}>
        <div className="container">
          <div className="row">
            <div className="col-md-12">
              <div className="common-page-header">
                <div className="common-page-head-section">
                  <div className="main-heading">
                    <h2>
                      {t("employee_management_heading")} <span>{departmentName || t("department_default")}</span>
                    </h2>
                    <div className="right-action">
                      <div style={{ minWidth: "255px" }}>
                        <InputWrapper className="mb-0 w-100">
                          <div className="icon-align right">
                            <Textbox
                              className="form-control w-100"
                              control={control}
                              name="search"
                              type="text"
                              placeholder={t("search_placeholder")}
                              onChange={(e) => {
                                const inputValue = e.target.value;
                                const trimmedValue = inputValue.trim();
                                const currentSearchTerm = searchStr.trim();

                                // Only trigger search if:
                                // 1. Trimmed value has meaningful content (length > 0), OR
                                // 2. Input is completely empty and we had a previous search (to reset)
                                if (trimmedValue.length > 0 || (inputValue === "" && currentSearchTerm.length > 0)) {
                                  debouncedHandleSearchInputChange(inputValue);
                                }
                              }}
                              onKeyDown={handleKeyDown}
                              onPaste={(e) => {
                                const pastedText = e.clipboardData.getData("text");
                                if (TYPING_PREVENTION_CHARACTERS.some((char) => pastedText.includes(char))) {
                                  e.preventDefault();
                                }
                              }}
                            >
                              <InputWrapper.Icon>
                                <SearchIcon />
                              </InputWrapper.Icon>
                            </Textbox>
                          </div>
                        </InputWrapper>
                      </div>
                      <Button
                        className="primary-btn rounded-md button-sm"
                        onClick={() => {
                          router.push(
                            `${ROUTES.ROLE_EMPLOYEES.ADD_EMPLOYEE}?departmentId=${encodeURIComponent(encryptedDeptId)}&departmentName=${encodeURIComponent(departmentName)}`
                          );
                        }}
                      >
                        {t("employee_add_btn")}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              <div className="table-responsive" style={{ display: "flex", flexDirection: "column" }}>
                <div id="scrollableUserListDiv">
                  {/* Fixed header table */}
                  <div style={{ overflow: "hidden" }}>
                    <table className="table overflow-auto" style={{ tableLayout: "fixed", marginBottom: 0 }}>
                      <thead>
                        <tr>
                          <th style={{ width: "16%" }}>{t("employee")}</th>
                          <th style={{ width: "16%" }}>{t("email")}</th>
                          <th style={{ width: "16%" }}>{t("role")}</th>
                          <th style={{ width: "16%" }} className="text-center">
                            {t("status")}
                          </th>
                          <th style={{ width: "16%" }} className="text-center">
                            {t("interview_order")}
                          </th>
                          <th style={{ width: "16%" }} className="text-center">
                            {t("actions")}
                          </th>
                        </tr>
                      </thead>
                    </table>
                  </div>

                  {/* Scrollable body */}
                  <div style={{ flex: 1 }}>
                    <InfiniteScroll
                      dataLength={employees?.length}
                      next={() => fetchMoreEmployees(searchStr, offset)}
                      hasMore={hasMore}
                      // scrollThreshold={0.8}
                      // scrollableTarget="scrollableUserListDiv"
                      className="flex flex-col w-full"
                      style={{ marginTop: "-1px" }} // Remove gap between header and body
                      loader={
                        loading ? (
                          <table className="table w-100">
                            <TableSkeleton rows={4} cols={6} colWidths="120,80,100,24,24,24" />
                          </table>
                        ) : null
                      }
                      endMessage={
                        !loading &&
                        employees?.length > 0 && (
                          <table className="table w-100">
                            <tbody>
                              <tr>
                                <td colSpan={6} style={{ textAlign: "center", backgroundColor: "#fff" }}>
                                  {t("no_more_employees_to_fetch")}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        )
                      }
                      height={window.innerHeight - 310} // Adjusted to account for header height
                    >
                      <table className="table overflow-auto" style={{ tableLayout: "fixed", marginTop: 0 }}>
                        {employees.length > 0 ? (
                          <tbody>
                            {[...employees]
                              .sort((a, b) => {
                                // Sort by isActive (active first), then by interviewOrder
                                if (a.isActive !== b.isActive) {
                                  return b.isActive ? 1 : -1;
                                }
                                return (a.interviewOrder || 0) - (b.interviewOrder || 0);
                              })
                              .map((employee: EmployeeInterface) => (
                                <tr key={employee.id}>
                                  <td style={{ width: "16%" }}>
                                    {employee.firstName || employee.lastName
                                      ? `${toTitleCase(employee.firstName.trim().substring(0, 15))} ${employee.lastName.trim().length > 15 ? toTitleCase(employee.lastName.trim().substring(0, 15)) + "..." : toTitleCase(employee.lastName.trim())}`
                                      : "--"}
                                  </td>
                                  <td style={{ width: "16%" }}>
                                    <a href={`mailto:${employee.email}`} className="email-text">
                                      {employee.email && employee.email.length > 18 ? employee.email.substring(0, 18) + "..." : employee.email}
                                    </a>
                                  </td>
                                  <td style={{ width: "16%" }}>
                                    <div className={styles.role_selector}>
                                      <select
                                        className={styles.role_select}
                                        value={employee.selectedRole.id}
                                        onChange={(e) => prepareRoleChange(employee.id, parseInt(e.target.value))}
                                        disabled={updatingRoleId === employee.id || employee.isAdmin}
                                      >
                                        {roles.map((role) => (
                                          <option key={role.id} value={role.id} disabled={role.id === employee.selectedRole.id}>
                                            {role.name}
                                          </option>
                                        ))}
                                      </select>
                                    </div>
                                  </td>
                                  <td align="center" style={{ width: "16%" }} className={employee.isActive ? "text-green" : "text-danger"}>
                                    {employee.isActive ? t("active") : t("inactive")}
                                  </td>
                                  <td align="center" style={{ width: "16%" }}>
                                    {employee.interviewOrder}
                                  </td>
                                  <td style={{ width: "16%" }}>
                                    <div className="position-relative">
                                      <Button className="clear-btn p-0 m-auto" onClick={() => toggleActionDropdown(employee.id)}>
                                        <ThreeDotsIcon />
                                      </Button>

                                      {openActionId === employee.id && (
                                        <ul className="custom-dropdown" onClick={(e) => e.stopPropagation()}>
                                          {employee.isActive && (
                                            <li
                                              onClick={() => {
                                                setOpenActionId(null); // Close dropdown
                                                prepareOrderChange(employee);
                                              }}
                                            >
                                              {t("update_order_of_interview")}
                                            </li>
                                          )}
                                          {/* <li className={styles.dropdown_item} onClick={() => setOpenActionId(null)}>
                                        {t("assign_interview")}
                                      </li> */}
                                          {/* {!employee.isAdmin ? (
                                          <li onClick={() => setOpenActionId(null)}>
                                            {t("remove")}
                                          </li>
                                        ) : null} */}
                                          {!employee.isAdmin ? (
                                            <li
                                              onClick={() => {
                                                setOpenActionId(null);
                                                setStatusConfirmModal({
                                                  show: true,
                                                  employeeId: employee.id,
                                                  status: !employee.isActive,
                                                });
                                              }}
                                            >
                                              {employee.isActive ? t("inactive") : t("active")}
                                            </li>
                                          ) : null}
                                        </ul>
                                      )}
                                    </div>
                                  </td>
                                </tr>
                              ))}
                          </tbody>
                        ) : (
                          !loading && (
                            <tbody>
                              <tr>
                                <td colSpan={5} style={{ textAlign: "center" }}>
                                  {t("no_employees")}
                                </td>
                              </tr>
                            </tbody>
                          )
                        )}
                      </table>
                    </InfiniteScroll>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Role Change Confirmation Modal */}
      {roleChangeModal.show && (
        <ChangeRoleModal
          employeeName={roleChangeModal.employeeName}
          currentRole={roleChangeModal.currentRole}
          newRole={roleChangeModal.newRoleName}
          onConfirm={handleRoleChange}
          onCancel={cancelRoleChange}
          isLoading={updatingRoleId === roleChangeModal.employeeId}
        />
      )}

      {/* Interview Order Update Modal */}
      {orderModal.show && (
        <UpdateInterviewOrderModal
          employeeName={orderModal.employeeName}
          currentOrder={orderModal.currentOrder}
          onConfirm={handleOrderChange}
          onCancel={cancelOrderChange}
          isLoading={updatingOrderId === orderModal.employeeId}
          employees={employees || []}
        />
      )}

      {/* Remove Employee Modal */}
      {removeEmployeeModal.show && (
        <RemoveEmployeeModal employee={removeEmployeeModal.employee} onSubmitSuccess={handleEmployeeRemoval} onClickCancel={cancelEmployeeRemoval} />
      )}

      {/* Status Change Confirmation Modal */}
      {statusConfirmModal.show && (
        <ConfirmationModal
          isOpen={statusConfirmModal.show}
          title={t("confirm_status_change_title")}
          message={t("confirm_status_change_message")}
          confirmButtonText={t("confirm")}
          // cancelButtonText={t("cancel")}
          onConfirm={UpdateEmployeeStatus}
          onClose={() => setStatusConfirmModal({ show: false, employeeId: null, status: false })}
          loading={statusLoading}
        />
      )}
    </>
  );
};

export default EmployeeManagementDetail;
